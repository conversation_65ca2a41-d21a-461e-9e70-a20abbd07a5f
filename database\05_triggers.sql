-- =============================================================================
-- FSNC Dashboard Database Setup - Triggers
-- =============================================================================
-- This script creates all database triggers for automated processes including
-- timestamp updates, audit logging, and invitation handling.
--
-- Run this script after 04_functions.sql
-- =============================================================================

-- =============================================================================
-- TIMESTAMP UPDATE TRIGGERS
-- =============================================================================

-- Generic function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply updated_at triggers to all relevant tables
CREATE TRIGGER trigger_profiles_updated_at
  BEFORE UPDATE ON profiles
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_invitations_updated_at
  BEFORE UPDATE ON invitations
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_categories_updated_at
  BEFORE UPDATE ON categories
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_tags_updated_at
  BEFORE UPDATE ON tags
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_posts_updated_at
  BEFORE UPDATE ON posts
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_pages_updated_at
  BEFORE UPDATE ON pages
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_biographies_updated_at
  BEFORE UPDATE ON biographies
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_organization_settings_updated_at
  BEFORE UPDATE ON organization_settings
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- =============================================================================
-- AUDIT LOGGING TRIGGERS
-- =============================================================================

-- Generic audit logging function
CREATE OR REPLACE FUNCTION audit_trigger_function()
RETURNS TRIGGER AS $$
DECLARE
  current_user_id UUID;
  audit_action_type audit_action;
  old_values JSONB;
  new_values JSONB;
BEGIN
  -- Get current user ID
  current_user_id := auth.uid();
  
  -- Determine action type
  IF TG_OP = 'INSERT' THEN
    audit_action_type := 'INSERT';
    old_values := NULL;
    new_values := to_jsonb(NEW);
  ELSIF TG_OP = 'UPDATE' THEN
    audit_action_type := 'UPDATE';
    old_values := to_jsonb(OLD);
    new_values := to_jsonb(NEW);
  ELSIF TG_OP = 'DELETE' THEN
    audit_action_type := 'DELETE';
    old_values := to_jsonb(OLD);
    new_values := NULL;
  END IF;
  
  -- Insert audit log entry
  INSERT INTO audit_log (
    user_id,
    action,
    target_table_name,
    target_record_id,
    old_value,
    new_value,
    description
  ) VALUES (
    current_user_id,
    audit_action_type,
    TG_TABLE_NAME,
    COALESCE(NEW.id::TEXT, OLD.id::TEXT),
    old_values,
    new_values,
    TG_OP || ' operation on ' || TG_TABLE_NAME
  );
  
  -- Return appropriate record
  IF TG_OP = 'DELETE' THEN
    RETURN OLD;
  ELSE
    RETURN NEW;
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Apply audit triggers to key tables
CREATE TRIGGER trigger_profiles_audit
  AFTER INSERT OR UPDATE OR DELETE ON profiles
  FOR EACH ROW
  EXECUTE FUNCTION audit_trigger_function();

CREATE TRIGGER trigger_posts_audit
  AFTER INSERT OR UPDATE OR DELETE ON posts
  FOR EACH ROW
  EXECUTE FUNCTION audit_trigger_function();

CREATE TRIGGER trigger_pages_audit
  AFTER INSERT OR UPDATE OR DELETE ON pages
  FOR EACH ROW
  EXECUTE FUNCTION audit_trigger_function();

CREATE TRIGGER trigger_categories_audit
  AFTER INSERT OR UPDATE OR DELETE ON categories
  FOR EACH ROW
  EXECUTE FUNCTION audit_trigger_function();

CREATE TRIGGER trigger_tags_audit
  AFTER INSERT OR UPDATE OR DELETE ON tags
  FOR EACH ROW
  EXECUTE FUNCTION audit_trigger_function();

CREATE TRIGGER trigger_biographies_audit
  AFTER INSERT OR UPDATE OR DELETE ON biographies
  FOR EACH ROW
  EXECUTE FUNCTION audit_trigger_function();

CREATE TRIGGER trigger_invitations_audit
  AFTER INSERT OR UPDATE OR DELETE ON invitations
  FOR EACH ROW
  EXECUTE FUNCTION audit_trigger_function();

CREATE TRIGGER trigger_organization_settings_audit
  AFTER INSERT OR UPDATE OR DELETE ON organization_settings
  FOR EACH ROW
  EXECUTE FUNCTION audit_trigger_function();

-- =============================================================================
-- INVITATION HANDLING TRIGGER
-- =============================================================================

-- Trigger to handle new user registration with invitation validation
CREATE TRIGGER trigger_handle_new_user_with_invitation
  AFTER INSERT ON auth.users
  FOR EACH ROW
  EXECUTE FUNCTION handle_new_user_with_invitation();

-- =============================================================================
-- PROFILE SECURITY TRIGGERS
-- =============================================================================

-- Trigger to prevent non-admin users from changing their own role
CREATE TRIGGER trigger_prevent_role_change
  BEFORE UPDATE ON profiles
  FOR EACH ROW
  EXECUTE FUNCTION prevent_role_change();

-- Trigger to validate newsletter subscriber updates
CREATE TRIGGER trigger_validate_newsletter_update
  BEFORE UPDATE ON newsletter_subscribers
  FOR EACH ROW
  EXECUTE FUNCTION validate_newsletter_update();

-- =============================================================================
-- CONTENT VERSIONING TRIGGERS
-- =============================================================================

-- Function to automatically create post versions on significant changes
CREATE OR REPLACE FUNCTION auto_create_post_version()
RETURNS TRIGGER AS $$
DECLARE
  significant_change BOOLEAN := FALSE;
BEGIN
  -- Check if this is a significant change that warrants a new version
  IF OLD.title != NEW.title OR 
     OLD.content != NEW.content OR 
     OLD.status != NEW.status OR
     OLD.category_id != NEW.category_id THEN
    significant_change := TRUE;
  END IF;
  
  -- Create version if significant change detected
  IF significant_change THEN
    PERFORM create_post_version(
      NEW.id,
      auth.uid(),
      'Automatic version created due to significant content change'
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Apply versioning trigger to posts
CREATE TRIGGER trigger_posts_auto_version
  AFTER UPDATE ON posts
  FOR EACH ROW
  EXECUTE FUNCTION auto_create_post_version();

-- =============================================================================
-- SLUG GENERATION TRIGGERS
-- =============================================================================

-- Function to generate URL-friendly slugs
CREATE OR REPLACE FUNCTION generate_slug_from_title()
RETURNS TRIGGER AS $$
BEGIN
  -- Only generate slug if it's empty or null
  IF NEW.slug IS NULL OR NEW.slug = '' THEN
    NEW.slug := lower(
      regexp_replace(
        regexp_replace(
          regexp_replace(NEW.title, '[^a-zA-Z0-9\s-]', '', 'g'),
          '\s+', '-', 'g'
        ),
        '-+', '-', 'g'
      )
    );
    
    -- Trim leading/trailing hyphens
    NEW.slug := trim(both '-' from NEW.slug);
    
    -- Ensure slug is not empty
    IF NEW.slug = '' THEN
      NEW.slug := 'untitled-' || extract(epoch from now())::text;
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply slug generation triggers
CREATE TRIGGER trigger_posts_generate_slug
  BEFORE INSERT OR UPDATE ON posts
  FOR EACH ROW
  EXECUTE FUNCTION generate_slug_from_title();

CREATE TRIGGER trigger_pages_generate_slug
  BEFORE INSERT OR UPDATE ON pages
  FOR EACH ROW
  EXECUTE FUNCTION generate_slug_from_title();

CREATE TRIGGER trigger_categories_generate_slug
  BEFORE INSERT OR UPDATE ON categories
  FOR EACH ROW
  EXECUTE FUNCTION generate_slug_from_title();

CREATE TRIGGER trigger_tags_generate_slug
  BEFORE INSERT OR UPDATE ON tags
  FOR EACH ROW
  EXECUTE FUNCTION generate_slug_from_title();

-- =============================================================================
-- NEWSLETTER SUBSCRIPTION TRIGGERS
-- =============================================================================

-- Function to handle newsletter subscription events
CREATE OR REPLACE FUNCTION handle_newsletter_subscription_change()
RETURNS TRIGGER AS $$
BEGIN
  -- Log subscription confirmation
  IF TG_OP = 'UPDATE' AND OLD.is_active = FALSE AND NEW.is_active = TRUE THEN
    INSERT INTO audit_log (
      action,
      target_table_name,
      target_record_id,
      description
    ) VALUES (
      'NEWSLETTER_CONFIRMED',
      'newsletter_subscribers',
      NEW.id::TEXT,
      'Newsletter subscription confirmed for ' || NEW.email
    );
  END IF;
  
  -- Log unsubscription
  IF TG_OP = 'UPDATE' AND OLD.is_active = TRUE AND NEW.is_active = FALSE THEN
    INSERT INTO audit_log (
      action,
      target_table_name,
      target_record_id,
      description
    ) VALUES (
      'NEWSLETTER_UNSUBSCRIBE',
      'newsletter_subscribers',
      NEW.id::TEXT,
      'Newsletter unsubscription for ' || NEW.email
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Apply newsletter subscription trigger
CREATE TRIGGER trigger_newsletter_subscription_change
  AFTER UPDATE ON newsletter_subscribers
  FOR EACH ROW
  EXECUTE FUNCTION handle_newsletter_subscription_change();

-- =============================================================================
-- SCRIPT COMPLETION
-- =============================================================================

-- Log successful completion
DO $$
BEGIN
  RAISE NOTICE 'Database triggers created successfully at %', NOW();
END $$;
