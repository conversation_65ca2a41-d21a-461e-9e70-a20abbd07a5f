-- =============================================================================
-- FSNC Dashboard Database Setup - PostgreSQL Functions
-- =============================================================================
-- This script creates all PostgreSQL functions needed for business logic,
-- data validation, and automated processes.
--
-- Run this script after 03_tables.sql
-- =============================================================================

-- =============================================================================
-- INVITATION MANAGEMENT FUNCTIONS
-- =============================================================================

-- Function to handle new user registration from invitation
CREATE OR REPLACE FUNCTION handle_new_user_with_invitation()
RETURNS TRIGGER AS $$
DECLARE
  invitation_record invitations;
  user_email TEXT;
BEGIN
  -- Get the user's email from auth.users
  user_email := NEW.email;
  
  -- Find a valid invitation for this email
  SELECT * INTO invitation_record
  FROM invitations
  WHERE email = user_email
    AND status = 'pending'
    AND expires_at > NOW()
  ORDER BY created_at DESC
  LIMIT 1;
  
  -- If no valid invitation found, prevent registration
  IF invitation_record IS NULL THEN
    RAISE EXCEPTION 'No valid invitation found for email: %', user_email;
  END IF;
  
  -- Create profile with the role from invitation
  INSERT INTO profiles (id, email, role)
  VALUES (NEW.id, user_email, invitation_record.role_to_assign);
  
  -- Mark invitation as accepted
  UPDATE invitations
  SET status = 'accepted',
      updated_at = NOW()
  WHERE id = invitation_record.id;
  
  -- Log the invitation acceptance
  INSERT INTO audit_log (user_id, action, target_table_name, target_record_id, description)
  VALUES (NEW.id, 'INVITE_ACCEPTED', 'invitations', invitation_record.id::TEXT, 
          'User registered via invitation');
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to create a new invitation
CREATE OR REPLACE FUNCTION create_invitation(
  p_email TEXT,
  p_role user_role,
  p_invited_by_id UUID DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
  invitation_id UUID;
  existing_user_id UUID;
BEGIN
  -- Check if user already exists
  SELECT id INTO existing_user_id
  FROM profiles
  WHERE email = p_email AND deleted_at IS NULL;
  
  IF existing_user_id IS NOT NULL THEN
    RAISE EXCEPTION 'User with email % already exists', p_email;
  END IF;
  
  -- Check if there's already a pending invitation
  IF EXISTS (
    SELECT 1 FROM invitations
    WHERE email = p_email
      AND status = 'pending'
      AND expires_at > NOW()
  ) THEN
    RAISE EXCEPTION 'Pending invitation already exists for email %', p_email;
  END IF;
  
  -- Create the invitation
  INSERT INTO invitations (email, role_to_assign, invited_by_id)
  VALUES (p_email, p_role, p_invited_by_id)
  RETURNING id INTO invitation_id;
  
  -- Log the invitation creation
  INSERT INTO audit_log (user_id, action, target_table_name, target_record_id, description)
  VALUES (p_invited_by_id, 'INVITE_SENT', 'invitations', invitation_id::TEXT, 
          'Invitation sent to ' || p_email);
  
  RETURN invitation_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to validate invitation token
CREATE OR REPLACE FUNCTION validate_invitation_token(p_token TEXT)
RETURNS TABLE (
  invitation_id UUID,
  email TEXT,
  role_to_assign user_role,
  is_valid BOOLEAN
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    i.id,
    i.email,
    i.role_to_assign,
    (i.status = 'pending' AND i.expires_at > NOW()) as is_valid
  FROM invitations i
  WHERE i.token = p_token;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================================================
-- CONTENT MANAGEMENT FUNCTIONS
-- =============================================================================

-- Function to create a new post version
CREATE OR REPLACE FUNCTION create_post_version(
  p_post_id UUID,
  p_edited_by_id UUID,
  p_reason_for_change TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
  version_id UUID;
  current_post posts;
  new_version_number INTEGER;
BEGIN
  -- Get current post data
  SELECT * INTO current_post
  FROM posts
  WHERE id = p_post_id AND deleted_at IS NULL;
  
  IF current_post IS NULL THEN
    RAISE EXCEPTION 'Post not found: %', p_post_id;
  END IF;
  
  -- Calculate new version number
  new_version_number := current_post.current_version + 1;
  
  -- Create version record
  INSERT INTO post_versions (
    post_id, version_number, title, slug, content, excerpt,
    featured_image_url, status, published_at, category_id,
    meta_title, meta_description, edited_by_id, reason_for_change
  )
  VALUES (
    current_post.id, new_version_number, current_post.title, current_post.slug,
    current_post.content, current_post.excerpt, current_post.featured_image_url,
    current_post.status, current_post.published_at, current_post.category_id,
    current_post.meta_title, current_post.meta_description, p_edited_by_id,
    p_reason_for_change
  )
  RETURNING id INTO version_id;
  
  -- Update post's current version
  UPDATE posts
  SET current_version = new_version_number,
      last_edited_by_id = p_edited_by_id,
      updated_at = NOW()
  WHERE id = p_post_id;
  
  RETURN version_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to publish content (posts or pages)
CREATE OR REPLACE FUNCTION publish_content(
  p_table_name TEXT,
  p_content_id UUID,
  p_published_by_id UUID
)
RETURNS BOOLEAN AS $$
DECLARE
  sql_query TEXT;
BEGIN
  -- Validate table name
  IF p_table_name NOT IN ('posts', 'pages') THEN
    RAISE EXCEPTION 'Invalid table name: %', p_table_name;
  END IF;
  
  -- Build and execute dynamic SQL
  sql_query := format(
    'UPDATE %I SET status = ''published'', published_at = NOW(), last_edited_by_id = $1, updated_at = NOW() WHERE id = $2 AND deleted_at IS NULL',
    p_table_name
  );
  
  EXECUTE sql_query USING p_published_by_id, p_content_id;
  
  -- Log the publication
  INSERT INTO audit_log (user_id, action, target_table_name, target_record_id, description)
  VALUES (p_published_by_id, 'UPDATE', p_table_name, p_content_id::TEXT, 
          'Content published');
  
  RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================================================
-- NEWSLETTER MANAGEMENT FUNCTIONS
-- =============================================================================

-- Function to subscribe to newsletter
CREATE OR REPLACE FUNCTION subscribe_to_newsletter(
  p_email TEXT,
  p_first_name TEXT DEFAULT NULL,
  p_last_name TEXT DEFAULT NULL,
  p_source TEXT DEFAULT 'website'
)
RETURNS UUID AS $$
DECLARE
  subscriber_id UUID;
  existing_subscriber newsletter_subscribers;
BEGIN
  -- Check if subscriber already exists
  SELECT * INTO existing_subscriber
  FROM newsletter_subscribers
  WHERE email = p_email;
  
  IF existing_subscriber IS NOT NULL THEN
    -- If already active, return existing ID
    IF existing_subscriber.is_active THEN
      RETURN existing_subscriber.id;
    END IF;
    
    -- If not active, update and resend confirmation
    UPDATE newsletter_subscribers
    SET first_name = COALESCE(p_first_name, first_name),
        last_name = COALESCE(p_last_name, last_name),
        source = p_source,
        confirmation_token = generate_secure_token(32),
        confirmation_token_expires_at = NOW() + INTERVAL '24 hours',
        created_at = NOW()
    WHERE id = existing_subscriber.id
    RETURNING id INTO subscriber_id;
  ELSE
    -- Create new subscriber
    INSERT INTO newsletter_subscribers (email, first_name, last_name, source)
    VALUES (p_email, p_first_name, p_last_name, p_source)
    RETURNING id INTO subscriber_id;
  END IF;
  
  -- Log the subscription attempt
  INSERT INTO audit_log (action, target_table_name, target_record_id, description)
  VALUES ('NEWSLETTER_SUBSCRIBE_ATTEMPT', 'newsletter_subscribers', subscriber_id::TEXT, 
          'Newsletter subscription attempt for ' || p_email);
  
  RETURN subscriber_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to confirm newsletter subscription
CREATE OR REPLACE FUNCTION confirm_newsletter_subscription(p_token TEXT)
RETURNS BOOLEAN AS $$
DECLARE
  subscriber_record newsletter_subscribers;
BEGIN
  -- Find subscriber by token
  SELECT * INTO subscriber_record
  FROM newsletter_subscribers
  WHERE confirmation_token = p_token
    AND confirmation_token_expires_at > NOW()
    AND NOT is_active;
  
  IF subscriber_record IS NULL THEN
    RETURN FALSE;
  END IF;
  
  -- Activate subscription
  UPDATE newsletter_subscribers
  SET is_active = true,
      confirmed_at = NOW(),
      confirmation_token = NULL,
      confirmation_token_expires_at = NULL
  WHERE id = subscriber_record.id;
  
  -- Log the confirmation
  INSERT INTO audit_log (action, target_table_name, target_record_id, description)
  VALUES ('NEWSLETTER_CONFIRMED', 'newsletter_subscribers', subscriber_record.id::TEXT, 
          'Newsletter subscription confirmed for ' || subscriber_record.email);
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================================================
-- UTILITY FUNCTIONS
-- =============================================================================

-- Function to soft delete a record
CREATE OR REPLACE FUNCTION soft_delete_record(
  p_table_name TEXT,
  p_record_id UUID,
  p_deleted_by_id UUID
)
RETURNS BOOLEAN AS $$
DECLARE
  sql_query TEXT;
BEGIN
  -- Validate table name (only allow tables that support soft delete)
  IF p_table_name NOT IN ('profiles', 'posts', 'pages', 'categories', 'tags', 'biographies') THEN
    RAISE EXCEPTION 'Soft delete not supported for table: %', p_table_name;
  END IF;

  -- Build and execute dynamic SQL
  sql_query := format(
    'UPDATE %I SET deleted_at = NOW(), updated_at = NOW() WHERE id = $1 AND deleted_at IS NULL',
    p_table_name
  );

  EXECUTE sql_query USING p_record_id;

  -- Log the deletion
  INSERT INTO audit_log (user_id, action, target_table_name, target_record_id, description)
  VALUES (p_deleted_by_id, 'DELETE', p_table_name, p_record_id::TEXT,
          'Record soft deleted');

  RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to restore a soft deleted record
CREATE OR REPLACE FUNCTION restore_soft_deleted_record(
  p_table_name TEXT,
  p_record_id UUID,
  p_restored_by_id UUID
)
RETURNS BOOLEAN AS $$
DECLARE
  sql_query TEXT;
BEGIN
  -- Validate table name
  IF p_table_name NOT IN ('profiles', 'posts', 'pages', 'categories', 'tags', 'biographies') THEN
    RAISE EXCEPTION 'Soft delete restore not supported for table: %', p_table_name;
  END IF;

  -- Build and execute dynamic SQL
  sql_query := format(
    'UPDATE %I SET deleted_at = NULL, updated_at = NOW() WHERE id = $1 AND deleted_at IS NOT NULL',
    p_table_name
  );

  EXECUTE sql_query USING p_record_id;

  -- Log the restoration
  INSERT INTO audit_log (user_id, action, target_table_name, target_record_id, description)
  VALUES (p_restored_by_id, 'UPDATE', p_table_name, p_record_id::TEXT,
          'Record restored from soft delete');

  RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to clean up expired invitations
CREATE OR REPLACE FUNCTION cleanup_expired_invitations()
RETURNS INTEGER AS $$
DECLARE
  expired_count INTEGER;
BEGIN
  -- Update expired invitations
  UPDATE invitations
  SET status = 'expired',
      updated_at = NOW()
  WHERE status = 'pending'
    AND expires_at <= NOW();

  GET DIAGNOSTICS expired_count = ROW_COUNT;

  -- Log the cleanup if any invitations were expired
  IF expired_count > 0 THEN
    INSERT INTO audit_log (action, target_table_name, description)
    VALUES ('UPDATE', 'invitations',
            'Cleaned up ' || expired_count || ' expired invitations');
  END IF;

  RETURN expired_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get content statistics
CREATE OR REPLACE FUNCTION get_content_statistics()
RETURNS TABLE (
  total_posts BIGINT,
  published_posts BIGINT,
  draft_posts BIGINT,
  total_pages BIGINT,
  published_pages BIGINT,
  total_categories BIGINT,
  total_tags BIGINT,
  total_users BIGINT,
  active_newsletter_subscribers BIGINT
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    (SELECT COUNT(*) FROM posts WHERE deleted_at IS NULL),
    (SELECT COUNT(*) FROM posts WHERE status = 'published' AND deleted_at IS NULL),
    (SELECT COUNT(*) FROM posts WHERE status = 'draft' AND deleted_at IS NULL),
    (SELECT COUNT(*) FROM pages WHERE deleted_at IS NULL),
    (SELECT COUNT(*) FROM pages WHERE status = 'published' AND deleted_at IS NULL),
    (SELECT COUNT(*) FROM categories WHERE deleted_at IS NULL),
    (SELECT COUNT(*) FROM tags WHERE deleted_at IS NULL),
    (SELECT COUNT(*) FROM profiles WHERE deleted_at IS NULL),
    (SELECT COUNT(*) FROM newsletter_subscribers WHERE is_active = true);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================================================
-- PROFILE SECURITY FUNCTIONS
-- =============================================================================

-- Function to prevent non-admin users from changing their own role
CREATE OR REPLACE FUNCTION prevent_role_change()
RETURNS TRIGGER AS $$
DECLARE
  current_user_role user_role;
BEGIN
  -- Get current user's role
  SELECT role INTO current_user_role
  FROM profiles
  WHERE id = auth.uid() AND deleted_at IS NULL;

  -- If role is being changed and user is not admin, prevent the change
  IF OLD.role != NEW.role AND current_user_role != 'admin' THEN
    RAISE EXCEPTION 'Only administrators can change user roles';
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to validate newsletter subscriber updates for public confirmation
CREATE OR REPLACE FUNCTION validate_newsletter_update()
RETURNS TRIGGER AS $$
BEGIN
  -- If this is a public confirmation (no authenticated user)
  IF auth.uid() IS NULL THEN
    -- Only allow changes to confirmation-related fields
    IF OLD.email != NEW.email OR
       OLD.first_name != NEW.first_name OR
       OLD.last_name != NEW.last_name THEN
      RAISE EXCEPTION 'Public confirmation can only update confirmation status, not personal information';
    END IF;

    -- Ensure this is actually a confirmation operation
    IF OLD.confirmation_token IS NULL OR NEW.confirmation_token IS NOT NULL THEN
      RAISE EXCEPTION 'Invalid confirmation operation';
    END IF;
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================================================
-- SCRIPT COMPLETION
-- =============================================================================

-- Log successful completion
DO $$
BEGIN
  RAISE NOTICE 'Database functions created successfully at %', NOW();
END $$;
