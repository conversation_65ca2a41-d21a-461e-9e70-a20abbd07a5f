-- =============================================================================
-- FSNC Dashboard Database - RLS Policy Testing Script
-- =============================================================================
-- This script tests the Row Level Security policies to ensure they work correctly
-- and don't contain any invalid OLD/NEW variable references.
--
-- Run this script after setting up the database to verify RLS policies.
-- =============================================================================

-- =============================================================================
-- TEST SETUP
-- =============================================================================

DO $$
BEGIN
  RAISE NOTICE '=============================================================================';
  RAISE NOTICE 'Starting RLS Policy Tests at %', NOW();
  RAISE NOTICE '=============================================================================';
END $$;

-- =============================================================================
-- TEST 1: VERIFY ALL POLICIES EXIST AND ARE VALID
-- =============================================================================

DO $$
DECLARE
  policy_count INTEGER;
  invalid_policies TEXT[];
BEGIN
  RAISE NOTICE 'Test 1: Checking policy existence and syntax...';
  
  -- Count total policies
  SELECT COUNT(*) INTO policy_count
  FROM pg_policies 
  WHERE schemaname = 'public';
  
  RAISE NOTICE 'Total RLS policies found: %', policy_count;
  
  -- Check for any policies that might have syntax issues
  -- This is a basic check - actual syntax errors would prevent policy creation
  IF policy_count < 20 THEN
    RAISE WARNING 'Expected at least 20 policies, found %', policy_count;
  ELSE
    RAISE NOTICE '✓ Policy count looks good';
  END IF;
END $$;

-- =============================================================================
-- TEST 2: VERIFY TABLE RLS IS ENABLED
-- =============================================================================

DO $$
DECLARE
  table_name TEXT;
  rls_enabled BOOLEAN;
  tables_without_rls TEXT[] := '{}';
BEGIN
  RAISE NOTICE 'Test 2: Checking RLS is enabled on all tables...';
  
  FOR table_name IN 
    SELECT tablename 
    FROM pg_tables 
    WHERE schemaname = 'public' 
    AND tablename NOT LIKE 'pg_%'
  LOOP
    SELECT relrowsecurity INTO rls_enabled
    FROM pg_class c
    JOIN pg_namespace n ON c.relnamespace = n.oid
    WHERE n.nspname = 'public' AND c.relname = table_name;
    
    IF NOT rls_enabled THEN
      tables_without_rls := array_append(tables_without_rls, table_name);
    END IF;
  END LOOP;
  
  IF array_length(tables_without_rls, 1) > 0 THEN
    RAISE WARNING 'Tables without RLS enabled: %', array_to_string(tables_without_rls, ', ');
  ELSE
    RAISE NOTICE '✓ RLS is enabled on all tables';
  END IF;
END $$;

-- =============================================================================
-- TEST 3: TEST BASIC POLICY FUNCTIONALITY
-- =============================================================================

DO $$
DECLARE
  test_category_id UUID;
  test_tag_id UUID;
  accessible_count INTEGER;
BEGIN
  RAISE NOTICE 'Test 3: Testing basic policy functionality...';
  
  -- Test that we can access seed data (should be accessible to all authenticated users)
  SELECT COUNT(*) INTO accessible_count FROM categories WHERE deleted_at IS NULL;
  RAISE NOTICE 'Accessible categories: %', accessible_count;
  
  SELECT COUNT(*) INTO accessible_count FROM tags WHERE deleted_at IS NULL;
  RAISE NOTICE 'Accessible tags: %', accessible_count;
  
  SELECT COUNT(*) INTO accessible_count FROM organization_settings;
  RAISE NOTICE 'Accessible organization settings: %', accessible_count;
  
  IF accessible_count > 0 THEN
    RAISE NOTICE '✓ Basic read access working';
  ELSE
    RAISE WARNING 'No organization settings accessible - this might indicate a policy issue';
  END IF;
END $$;

-- =============================================================================
-- TEST 4: TEST FUNCTION DEPENDENCIES
-- =============================================================================

DO $$
DECLARE
  function_exists BOOLEAN;
  function_name TEXT;
  missing_functions TEXT[] := '{}';
BEGIN
  RAISE NOTICE 'Test 4: Checking required functions exist...';
  
  -- Check for required functions used in RLS policies
  FOR function_name IN VALUES 
    ('user_has_role'),
    ('can_access_resource'),
    ('get_current_user_profile'),
    ('prevent_role_change'),
    ('validate_newsletter_update')
  LOOP
    SELECT EXISTS (
      SELECT 1 FROM information_schema.routines 
      WHERE routine_schema = 'public' 
      AND routine_name = function_name
      AND routine_type = 'FUNCTION'
    ) INTO function_exists;
    
    IF NOT function_exists THEN
      missing_functions := array_append(missing_functions, function_name);
    END IF;
  END LOOP;
  
  IF array_length(missing_functions, 1) > 0 THEN
    RAISE WARNING 'Missing required functions: %', array_to_string(missing_functions, ', ');
  ELSE
    RAISE NOTICE '✓ All required functions exist';
  END IF;
END $$;

-- =============================================================================
-- TEST 5: VERIFY NO OLD/NEW REFERENCES IN POLICIES
-- =============================================================================

DO $$
DECLARE
  policy_record RECORD;
  policies_with_old_new TEXT[] := '{}';
BEGIN
  RAISE NOTICE 'Test 5: Checking for invalid OLD/NEW references in policies...';
  
  -- Check policy definitions for OLD/NEW references
  FOR policy_record IN 
    SELECT schemaname, tablename, policyname, qual, with_check
    FROM pg_policies 
    WHERE schemaname = 'public'
  LOOP
    -- Check USING clause for OLD/NEW references
    IF policy_record.qual IS NOT NULL AND 
       (policy_record.qual LIKE '%OLD.%' OR policy_record.qual LIKE '%NEW.%') THEN
      policies_with_old_new := array_append(policies_with_old_new, 
        policy_record.tablename || '.' || policy_record.policyname || ' (USING)');
    END IF;
    
    -- Check WITH CHECK clause for OLD/NEW references
    IF policy_record.with_check IS NOT NULL AND 
       (policy_record.with_check LIKE '%OLD.%' OR policy_record.with_check LIKE '%NEW.%') THEN
      policies_with_old_new := array_append(policies_with_old_new, 
        policy_record.tablename || '.' || policy_record.policyname || ' (WITH CHECK)');
    END IF;
  END LOOP;
  
  IF array_length(policies_with_old_new, 1) > 0 THEN
    RAISE WARNING 'Policies with invalid OLD/NEW references: %', 
      array_to_string(policies_with_old_new, ', ');
  ELSE
    RAISE NOTICE '✓ No invalid OLD/NEW references found in policies';
  END IF;
END $$;

-- =============================================================================
-- TEST 6: TEST POLICY COVERAGE
-- =============================================================================

DO $$
DECLARE
  table_record RECORD;
  policy_count INTEGER;
  tables_without_policies TEXT[] := '{}';
BEGIN
  RAISE NOTICE 'Test 6: Checking policy coverage for all tables...';
  
  FOR table_record IN 
    SELECT tablename 
    FROM pg_tables 
    WHERE schemaname = 'public' 
    AND tablename NOT LIKE 'pg_%'
  LOOP
    SELECT COUNT(*) INTO policy_count
    FROM pg_policies 
    WHERE schemaname = 'public' AND tablename = table_record.tablename;
    
    IF policy_count = 0 THEN
      tables_without_policies := array_append(tables_without_policies, table_record.tablename);
    END IF;
  END LOOP;
  
  IF array_length(tables_without_policies, 1) > 0 THEN
    RAISE WARNING 'Tables without any RLS policies: %', 
      array_to_string(tables_without_policies, ', ');
  ELSE
    RAISE NOTICE '✓ All tables have RLS policies';
  END IF;
END $$;

-- =============================================================================
-- TEST SUMMARY
-- =============================================================================

DO $$
BEGIN
  RAISE NOTICE '=============================================================================';
  RAISE NOTICE 'RLS Policy Tests Completed at %', NOW();
  RAISE NOTICE '=============================================================================';
  RAISE NOTICE '';
  RAISE NOTICE 'If you see any warnings above, please review and fix the indicated issues.';
  RAISE NOTICE 'All tests passing indicates that RLS policies are properly configured.';
  RAISE NOTICE '';
  RAISE NOTICE 'Next steps:';
  RAISE NOTICE '1. Test with actual user accounts and different roles';
  RAISE NOTICE '2. Verify application functionality with RLS enabled';
  RAISE NOTICE '3. Monitor performance with RLS policies active';
END $$;
