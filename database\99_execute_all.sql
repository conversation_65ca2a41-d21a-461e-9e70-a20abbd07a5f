-- =============================================================================
-- FSNC Dashboard Database Setup - Master Execution Script
-- =============================================================================
-- This script executes all database setup scripts in the correct order.
-- Run this script to completely rebuild the FSNC Dashboard database from scratch.
--
-- IMPORTANT: This will create a complete database schema. Make sure you're
-- running this on the correct database and have proper backups if needed.
-- =============================================================================

-- =============================================================================
-- SCRIPT EXECUTION LOG
-- =============================================================================

DO $$
BEGIN
  RAISE NOTICE '=============================================================================';
  RAISE NOTICE 'FSNC Dashboard Database Setup - Starting at %', NOW();
  RAISE NOTICE '=============================================================================';
END $$;

-- =============================================================================
-- STEP 1: EXTENSIONS AND SETUP
-- =============================================================================

DO $$
BEGIN
  RAISE NOTICE 'Step 1: Setting up extensions and utility functions...';
END $$;

\i 01_extensions_and_setup.sql

-- =============================================================================
-- STEP 2: CUSTOM ENUMS
-- =============================================================================

DO $$
BEGIN
  RAISE NOTICE 'Step 2: Creating custom enum types...';
END $$;

\i 02_enums.sql

-- =============================================================================
-- STEP 3: DATABASE TABLES
-- =============================================================================

DO $$
BEGIN
  RAISE NOTICE 'Step 3: Creating database tables...';
END $$;

\i 03_tables.sql

-- =============================================================================
-- STEP 4: POSTGRESQL FUNCTIONS
-- =============================================================================

DO $$
BEGIN
  RAISE NOTICE 'Step 4: Creating PostgreSQL functions...';
END $$;

\i 04_functions.sql

-- =============================================================================
-- STEP 5: DATABASE TRIGGERS
-- =============================================================================

DO $$
BEGIN
  RAISE NOTICE 'Step 5: Creating database triggers...';
END $$;

\i 05_triggers.sql

-- =============================================================================
-- STEP 6: ROW LEVEL SECURITY POLICIES
-- =============================================================================

DO $$
BEGIN
  RAISE NOTICE 'Step 6: Implementing Row Level Security policies...';
END $$;

\i 06_rls_policies.sql

-- =============================================================================
-- STEP 7: PERFORMANCE INDEXES
-- =============================================================================

DO $$
BEGIN
  RAISE NOTICE 'Step 7: Creating performance indexes...';
END $$;

\i 07_indexes.sql

-- =============================================================================
-- STEP 8: SEED DATA
-- =============================================================================

DO $$
BEGIN
  RAISE NOTICE 'Step 8: Inserting seed data...';
END $$;

\i 08_seed_data.sql

-- =============================================================================
-- FINAL VERIFICATION AND SUMMARY
-- =============================================================================

DO $$
DECLARE
  table_count INTEGER;
  function_count INTEGER;
  trigger_count INTEGER;
  policy_count INTEGER;
  index_count INTEGER;
  enum_count INTEGER;
  setting_count INTEGER;
BEGIN
  RAISE NOTICE '=============================================================================';
  RAISE NOTICE 'FSNC Dashboard Database Setup - Verification and Summary';
  RAISE NOTICE '=============================================================================';
  
  -- Count created objects
  SELECT COUNT(*) INTO table_count
  FROM information_schema.tables 
  WHERE table_schema = 'public' AND table_type = 'BASE TABLE';
  
  SELECT COUNT(*) INTO function_count
  FROM information_schema.routines 
  WHERE routine_schema = 'public' AND routine_type = 'FUNCTION';
  
  SELECT COUNT(*) INTO trigger_count
  FROM information_schema.triggers 
  WHERE trigger_schema = 'public';
  
  SELECT COUNT(*) INTO policy_count
  FROM pg_policies 
  WHERE schemaname = 'public';
  
  SELECT COUNT(*) INTO index_count
  FROM pg_indexes 
  WHERE schemaname = 'public';
  
  SELECT COUNT(*) INTO enum_count
  FROM pg_type 
  WHERE typtype = 'e' AND typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public');
  
  SELECT COUNT(*) INTO setting_count
  FROM organization_settings;
  
  -- Display summary
  RAISE NOTICE 'Database objects created successfully:';
  RAISE NOTICE '  - Tables: %', table_count;
  RAISE NOTICE '  - Custom Enums: %', enum_count;
  RAISE NOTICE '  - Functions: %', function_count;
  RAISE NOTICE '  - Triggers: %', trigger_count;
  RAISE NOTICE '  - RLS Policies: %', policy_count;
  RAISE NOTICE '  - Indexes: %', index_count;
  RAISE NOTICE '  - Organization Settings: %', setting_count;
  
  RAISE NOTICE '';
  RAISE NOTICE 'Core application tables:';
  RAISE NOTICE '  - profiles: % rows', (SELECT COUNT(*) FROM profiles);
  RAISE NOTICE '  - categories: % rows', (SELECT COUNT(*) FROM categories);
  RAISE NOTICE '  - tags: % rows', (SELECT COUNT(*) FROM tags);
  RAISE NOTICE '  - posts: % rows', (SELECT COUNT(*) FROM posts);
  RAISE NOTICE '  - pages: % rows', (SELECT COUNT(*) FROM pages);
  RAISE NOTICE '  - biographies: % rows', (SELECT COUNT(*) FROM biographies);
  RAISE NOTICE '  - invitations: % rows', (SELECT COUNT(*) FROM invitations);
  RAISE NOTICE '  - newsletter_subscribers: % rows', (SELECT COUNT(*) FROM newsletter_subscribers);
  RAISE NOTICE '  - audit_log: % rows', (SELECT COUNT(*) FROM audit_log);
  
  RAISE NOTICE '';
  RAISE NOTICE '=============================================================================';
  RAISE NOTICE 'FSNC Dashboard Database Setup - COMPLETED SUCCESSFULLY at %', NOW();
  RAISE NOTICE '=============================================================================';
  
  RAISE NOTICE '';
  RAISE NOTICE 'Next Steps:';
  RAISE NOTICE '1. Create your first admin user via Supabase Auth dashboard';
  RAISE NOTICE '2. Use the invitation system to add additional users';
  RAISE NOTICE '3. Customize organization settings via the admin interface';
  RAISE NOTICE '4. Start creating content (posts, pages, biographies)';
  RAISE NOTICE '5. Configure email settings for invitations and newsletters';
  
  RAISE NOTICE '';
  RAISE NOTICE 'Important Security Notes:';
  RAISE NOTICE '- Row Level Security (RLS) is enabled on all tables';
  RAISE NOTICE '- Invitation-based registration is enforced via triggers';
  RAISE NOTICE '- All user actions are logged in the audit_log table';
  RAISE NOTICE '- Soft deletes are implemented for content recovery';
  RAISE NOTICE '- Role changes are protected by database triggers';

END $$;

-- =============================================================================
-- OPTIONAL: RLS POLICY TESTING
-- =============================================================================

DO $$
BEGIN
  RAISE NOTICE '';
  RAISE NOTICE 'Running RLS policy validation tests...';
END $$;

\i test_rls_policies.sql
