# FSNC Dashboard Database Setup

This directory contains comprehensive SQL scripts to rebuild the entire FSNC Dashboard database from scratch. These scripts create a complete, production-ready database schema with proper security, performance optimizations, and initial data.

## 📁 Script Overview

| Script                        | Description                                                        |
| ----------------------------- | ------------------------------------------------------------------ |
| `01_extensions_and_setup.sql` | PostgreSQL extensions and utility functions                        |
| `02_enums.sql`                | Custom enum types for data consistency                             |
| `03_tables.sql`               | All database tables with constraints and relationships             |
| `04_functions.sql`            | Business logic functions for invitations, content management, etc. |
| `05_triggers.sql`             | Automated triggers for timestamps, audit logging, and workflows    |
| `06_rls_policies.sql`         | Row Level Security policies for data access control                |
| `07_indexes.sql`              | Performance optimization indexes                                   |
| `08_seed_data.sql`            | Initial data and default settings                                  |
| `test_rls_policies.sql`       | RLS policy validation and testing script                           |
| `99_execute_all.sql`          | Master script to run all scripts in order                          |

## 🚀 Quick Start

### Option 1: Run All Scripts at Once (Recommended)

```sql
-- In Supabase SQL Editor or psql
\i database/99_execute_all.sql
```

### Option 2: Run Scripts Individually

Execute the scripts in order:

```sql
\i database/01_extensions_and_setup.sql
\i database/02_enums.sql
\i database/03_tables.sql
\i database/04_functions.sql
\i database/05_triggers.sql
\i database/06_rls_policies.sql
\i database/07_indexes.sql
\i database/08_seed_data.sql
```

## 🔧 Setup Instructions

### Prerequisites

1. **Supabase Project**: Create a new Supabase project or ensure you have admin access to an existing one
2. **Database Access**: Ensure you have database admin privileges
3. **Backup**: If running on an existing database, create a backup first

### Step-by-Step Setup

1. **Access Supabase SQL Editor**

   - Go to your Supabase project dashboard
   - Navigate to "SQL Editor"

2. **Run the Master Script**

   ```sql
   -- Copy and paste the contents of 99_execute_all.sql
   -- Or use the \i command if your client supports it
   ```

3. **Verify Installation**

   - Check that all tables are created
   - Verify RLS policies are active
   - Confirm seed data is inserted

4. **Create First Admin User**
   - Use Supabase Auth dashboard to create the first user
   - The invitation system will handle subsequent user registrations

## 🏗️ Database Architecture

### Core Tables

- **`profiles`** - User profiles extending auth.users
- **`invitations`** - Invitation-based user registration
- **`posts`** - Blog posts with rich content
- **`pages`** - Static website pages
- **`categories`** - Content categorization
- **`tags`** - Content tagging system
- **`biographies`** - Team member profiles
- **`newsletter_subscribers`** - Newsletter management
- **`organization_settings`** - Application configuration
- **`audit_log`** - Comprehensive audit trail

### Key Features

#### 🔐 Security Features

- **Row Level Security (RLS)** on all tables with proper PostgreSQL syntax
- **Role-based access control** (Admin, Publisher, Editor, Member)
- **Invitation-only registration** with email validation
- **Comprehensive audit logging** for all actions
- **Soft deletes** for data recovery
- **Database triggers** to prevent unauthorized role changes
- **Field-level protection** for sensitive operations

#### ⚡ Performance Features

- **Optimized indexes** for common queries
- **Full-text search** capabilities
- **Trigram indexes** for fuzzy search
- **Composite indexes** for complex filtering
- **Partial indexes** for specific conditions

#### 🔄 Automation Features

- **Automatic timestamps** (created_at, updated_at)
- **Content versioning** for posts
- **Slug generation** from titles
- **Newsletter confirmation** workflow
- **Invitation expiration** handling

## 🎯 User Roles & Permissions

| Role          | Permissions                                                  |
| ------------- | ------------------------------------------------------------ |
| **Admin**     | Full system access, user management, settings, audit logs    |
| **Publisher** | Content creation/editing/publishing, category/tag management |
| **Editor**    | Content creation/editing, submit for review                  |
| **Member**    | Basic authenticated access, read-only for most content       |

## 📊 Default Data

The seed script creates:

- **Organization Settings**: 50+ configuration options
- **Categories**: General, News, Events, Resources, Blog
- **Tags**: Important, Featured, Update, Announcement, etc.
- **Pages**: About Us, Contact, Privacy Policy, Terms of Service
- **Sample Content**: Welcome post and admin biography

## 🔧 Customization

### Adding New Settings

```sql
INSERT INTO organization_settings (key, value, description) VALUES
  ('your_setting_key', '"your_value"', 'Description of your setting');
```

### Creating Custom Categories

```sql
INSERT INTO categories (name, slug, description) VALUES
  ('Your Category', 'your-category', 'Description of your category');
```

### Modifying RLS Policies

Edit `06_rls_policies.sql` to adjust access controls:

```sql
-- Example: Allow editors to publish directly
CREATE POLICY "posts_editor_publish" ON posts
  FOR UPDATE
  USING (author_id = auth.uid() AND user_has_role(auth.uid(), 'editor'));
```

## 🚨 Important Notes

### Security Considerations

1. **Never disable RLS** on production tables
2. **Review audit logs** regularly for suspicious activity
3. **Use strong passwords** for database access
4. **Limit service role key** usage to server-side only
5. **Regular backups** are essential

### Performance Considerations

1. **Monitor index usage** and adjust as needed
2. **Archive old audit logs** to maintain performance
3. **Optimize JSONB queries** for content fields
4. **Consider read replicas** for high-traffic applications

### Maintenance Tasks

1. **Clean up expired invitations** regularly
2. **Archive old audit logs** (default: 365 days)
3. **Monitor database size** and optimize as needed
4. **Update statistics** for query optimization

## 🐛 Troubleshooting

### Common Issues

1. **Permission Denied Errors**

   - Ensure you have admin privileges
   - Check RLS policies are correctly configured

2. **Function Not Found Errors**

   - Run scripts in the correct order
   - Ensure extensions are properly installed

3. **Trigger Errors**

   - Verify all functions exist before creating triggers
   - Check function signatures match trigger definitions

4. **RLS Policy Conflicts**

   - Review policy logic for overlaps
   - Test with different user roles

5. **OLD/NEW Variable Errors in RLS Policies**
   - RLS policies cannot use OLD/NEW variables (these are for triggers only)
   - Use database triggers for field-level validation instead
   - Run the test script to validate policy syntax

### Getting Help

1. Check the application logs for detailed error messages
2. Review Supabase documentation for RLS and triggers
3. Test policies with different user roles in development
4. Use `EXPLAIN ANALYZE` to debug performance issues

## 📝 Schema Updates

When updating the schema:

1. **Create migration scripts** for changes
2. **Test thoroughly** in development
3. **Backup production** before applying
4. **Update TypeScript types** after schema changes
5. **Document changes** in version control

## 🔄 Regenerating Types

After running these scripts, regenerate TypeScript types:

```bash
npx supabase gen types typescript --project-id YOUR_PROJECT_ID > src/types/database.types.ts
```

## 📚 Additional Resources

- [Supabase Documentation](https://supabase.com/docs)
- [PostgreSQL RLS Guide](https://www.postgresql.org/docs/current/ddl-rowsecurity.html)
- [Database Design Best Practices](https://www.postgresql.org/docs/current/ddl-constraints.html)
- [Performance Tuning Guide](https://www.postgresql.org/docs/current/performance-tips.html)
