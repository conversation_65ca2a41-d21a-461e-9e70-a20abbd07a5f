-- =============================================================================
-- FSNC Dashboard Database Setup - Row Level Security (RLS) Policies
-- =============================================================================
-- This script implements comprehensive Row Level Security policies to ensure
-- users can only access data they are authorized to see based on their roles.
--
-- Run this script after 05_triggers.sql
-- =============================================================================

-- =============================================================================
-- PROFILES TABLE RLS POLICIES
-- =============================================================================

-- Users can view their own profile and admins can view all profiles
CREATE POLICY "profiles_select_policy" ON profiles
  FOR SELECT
  USING (
    id = auth.uid() OR 
    user_has_role(auth.uid(), 'admin')
  );

-- Users can update their own profile, admins can update any profile
-- Note: Role changes are protected by a database trigger (prevent_role_change)
CREATE POLICY "profiles_update_policy" ON profiles
  FOR UPDATE
  USING (
    id = auth.uid() OR
    user_has_role(auth.uid(), 'admin')
  )
  WITH CHECK (
    -- Allow update if user is admin, or if user is updating their own profile
    user_has_role(auth.uid(), 'admin') OR
    id = auth.uid()
  );

-- Only admins can insert new profiles (typically done via invitation system)
CREATE POLICY "profiles_insert_policy" ON profiles
  FOR INSERT
  WITH CHECK (user_has_role(auth.uid(), 'admin'));

-- Only admins can delete profiles (soft delete)
CREATE POLICY "profiles_delete_policy" ON profiles
  FOR UPDATE
  USING (user_has_role(auth.uid(), 'admin'))
  WITH CHECK (user_has_role(auth.uid(), 'admin'));

-- =============================================================================
-- INVITATIONS TABLE RLS POLICIES
-- =============================================================================

-- Only admins can view invitations
CREATE POLICY "invitations_select_policy" ON invitations
  FOR SELECT
  USING (user_has_role(auth.uid(), 'admin'));

-- Only admins can create invitations
CREATE POLICY "invitations_insert_policy" ON invitations
  FOR INSERT
  WITH CHECK (user_has_role(auth.uid(), 'admin'));

-- Only admins can update invitations
CREATE POLICY "invitations_update_policy" ON invitations
  FOR UPDATE
  USING (user_has_role(auth.uid(), 'admin'))
  WITH CHECK (user_has_role(auth.uid(), 'admin'));

-- Only admins can delete invitations
CREATE POLICY "invitations_delete_policy" ON invitations
  FOR DELETE
  USING (user_has_role(auth.uid(), 'admin'));

-- =============================================================================
-- CATEGORIES TABLE RLS POLICIES
-- =============================================================================

-- All authenticated users can view categories
CREATE POLICY "categories_select_policy" ON categories
  FOR SELECT
  USING (auth.uid() IS NOT NULL AND deleted_at IS NULL);

-- Publishers and admins can create categories
CREATE POLICY "categories_insert_policy" ON categories
  FOR INSERT
  WITH CHECK (user_has_role(auth.uid(), 'publisher'));

-- Publishers and admins can update categories
CREATE POLICY "categories_update_policy" ON categories
  FOR UPDATE
  USING (user_has_role(auth.uid(), 'publisher'))
  WITH CHECK (user_has_role(auth.uid(), 'publisher'));

-- Only admins can delete categories
CREATE POLICY "categories_delete_policy" ON categories
  FOR UPDATE
  USING (user_has_role(auth.uid(), 'admin'))
  WITH CHECK (user_has_role(auth.uid(), 'admin'));

-- =============================================================================
-- TAGS TABLE RLS POLICIES
-- =============================================================================

-- All authenticated users can view tags
CREATE POLICY "tags_select_policy" ON tags
  FOR SELECT
  USING (auth.uid() IS NOT NULL AND deleted_at IS NULL);

-- Publishers and admins can create tags
CREATE POLICY "tags_insert_policy" ON tags
  FOR INSERT
  WITH CHECK (user_has_role(auth.uid(), 'publisher'));

-- Publishers and admins can update tags
CREATE POLICY "tags_update_policy" ON tags
  FOR UPDATE
  USING (user_has_role(auth.uid(), 'publisher'))
  WITH CHECK (user_has_role(auth.uid(), 'publisher'));

-- Only admins can delete tags
CREATE POLICY "tags_delete_policy" ON tags
  FOR UPDATE
  USING (user_has_role(auth.uid(), 'admin'))
  WITH CHECK (user_has_role(auth.uid(), 'admin'));

-- =============================================================================
-- POSTS TABLE RLS POLICIES
-- =============================================================================

-- Users can view published posts, or their own posts, or if they have publisher+ role
CREATE POLICY "posts_select_policy" ON posts
  FOR SELECT
  USING (
    deleted_at IS NULL AND (
      status = 'published' OR
      author_id = auth.uid() OR
      user_has_role(auth.uid(), 'publisher')
    )
  );

-- Editors and above can create posts
CREATE POLICY "posts_insert_policy" ON posts
  FOR INSERT
  WITH CHECK (user_has_role(auth.uid(), 'editor'));

-- Users can update their own posts, publishers can update any post
CREATE POLICY "posts_update_policy" ON posts
  FOR UPDATE
  USING (
    deleted_at IS NULL AND (
      author_id = auth.uid() OR
      user_has_role(auth.uid(), 'publisher')
    )
  )
  WITH CHECK (
    -- Editors can only submit for review, not publish directly
    (user_has_role(auth.uid(), 'editor') AND NOT user_has_role(auth.uid(), 'publisher') AND status != 'published') OR
    -- Publishers and admins can set any status
    user_has_role(auth.uid(), 'publisher')
  );

-- Only admins can delete posts
CREATE POLICY "posts_delete_policy" ON posts
  FOR UPDATE
  USING (user_has_role(auth.uid(), 'admin'))
  WITH CHECK (user_has_role(auth.uid(), 'admin'));

-- =============================================================================
-- POST_TAGS TABLE RLS POLICIES
-- =============================================================================

-- Users can view post tags if they can view the post
CREATE POLICY "post_tags_select_policy" ON post_tags
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM posts p
      WHERE p.id = post_id
        AND p.deleted_at IS NULL
        AND (
          p.status = 'published' OR
          p.author_id = auth.uid() OR
          user_has_role(auth.uid(), 'publisher')
        )
    )
  );

-- Users can manage post tags if they can edit the post
CREATE POLICY "post_tags_insert_policy" ON post_tags
  FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM posts p
      WHERE p.id = post_id
        AND p.deleted_at IS NULL
        AND (
          p.author_id = auth.uid() OR
          user_has_role(auth.uid(), 'publisher')
        )
    )
  );

CREATE POLICY "post_tags_delete_policy" ON post_tags
  FOR DELETE
  USING (
    EXISTS (
      SELECT 1 FROM posts p
      WHERE p.id = post_id
        AND p.deleted_at IS NULL
        AND (
          p.author_id = auth.uid() OR
          user_has_role(auth.uid(), 'publisher')
        )
    )
  );

-- =============================================================================
-- POST_VERSIONS TABLE RLS POLICIES
-- =============================================================================

-- Users can view versions of posts they can access
CREATE POLICY "post_versions_select_policy" ON post_versions
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM posts p
      WHERE p.id = post_id
        AND p.deleted_at IS NULL
        AND (
          p.status = 'published' OR
          p.author_id = auth.uid() OR
          user_has_role(auth.uid(), 'publisher')
        )
    )
  );

-- Post versions are created automatically via triggers, no direct insert policy needed
-- Only admins can delete post versions
CREATE POLICY "post_versions_delete_policy" ON post_versions
  FOR DELETE
  USING (user_has_role(auth.uid(), 'admin'));

-- =============================================================================
-- PAGES TABLE RLS POLICIES
-- =============================================================================

-- Users can view published pages, or their own pages, or if they have publisher+ role
CREATE POLICY "pages_select_policy" ON pages
  FOR SELECT
  USING (
    deleted_at IS NULL AND (
      status = 'published' OR
      author_id = auth.uid() OR
      user_has_role(auth.uid(), 'publisher')
    )
  );

-- Editors and above can create pages
CREATE POLICY "pages_insert_policy" ON pages
  FOR INSERT
  WITH CHECK (user_has_role(auth.uid(), 'editor'));

-- Users can update their own pages, publishers can update any page
CREATE POLICY "pages_update_policy" ON pages
  FOR UPDATE
  USING (
    deleted_at IS NULL AND (
      author_id = auth.uid() OR
      user_has_role(auth.uid(), 'publisher')
    )
  )
  WITH CHECK (
    -- Editors can only submit for review, not publish directly
    (user_has_role(auth.uid(), 'editor') AND NOT user_has_role(auth.uid(), 'publisher') AND status != 'published') OR
    -- Publishers and admins can set any status
    user_has_role(auth.uid(), 'publisher')
  );

-- Only admins can delete pages
CREATE POLICY "pages_delete_policy" ON pages
  FOR UPDATE
  USING (user_has_role(auth.uid(), 'admin'))
  WITH CHECK (user_has_role(auth.uid(), 'admin'));

-- =============================================================================
-- BIOGRAPHIES TABLE RLS POLICIES
-- =============================================================================

-- Users can view public biographies, or their own biographies, or if they have publisher+ role
CREATE POLICY "biographies_select_policy" ON biographies
  FOR SELECT
  USING (
    deleted_at IS NULL AND (
      is_public = true OR
      created_by_id = auth.uid() OR
      user_has_role(auth.uid(), 'publisher')
    )
  );

-- Editors and above can create biographies
CREATE POLICY "biographies_insert_policy" ON biographies
  FOR INSERT
  WITH CHECK (user_has_role(auth.uid(), 'editor'));

-- Users can update their own biographies, publishers can update any biography
CREATE POLICY "biographies_update_policy" ON biographies
  FOR UPDATE
  USING (
    deleted_at IS NULL AND (
      created_by_id = auth.uid() OR
      user_has_role(auth.uid(), 'publisher')
    )
  )
  WITH CHECK (
    created_by_id = auth.uid() OR
    user_has_role(auth.uid(), 'publisher')
  );

-- Only admins can delete biographies
CREATE POLICY "biographies_delete_policy" ON biographies
  FOR UPDATE
  USING (user_has_role(auth.uid(), 'admin'))
  WITH CHECK (user_has_role(auth.uid(), 'admin'));

-- =============================================================================
-- NEWSLETTER_SUBSCRIBERS TABLE RLS POLICIES
-- =============================================================================

-- Only admins and publishers can view newsletter subscribers
CREATE POLICY "newsletter_subscribers_select_policy" ON newsletter_subscribers
  FOR SELECT
  USING (user_has_role(auth.uid(), 'publisher'));

-- Newsletter subscriptions are typically created via public API, but allow publishers to add manually
CREATE POLICY "newsletter_subscribers_insert_policy" ON newsletter_subscribers
  FOR INSERT
  WITH CHECK (user_has_role(auth.uid(), 'publisher'));

-- Only admins and publishers can update newsletter subscribers
CREATE POLICY "newsletter_subscribers_update_policy" ON newsletter_subscribers
  FOR UPDATE
  USING (user_has_role(auth.uid(), 'publisher'))
  WITH CHECK (user_has_role(auth.uid(), 'publisher'));

-- Only admins can delete newsletter subscribers
CREATE POLICY "newsletter_subscribers_delete_policy" ON newsletter_subscribers
  FOR DELETE
  USING (user_has_role(auth.uid(), 'admin'));

-- =============================================================================
-- ORGANIZATION_SETTINGS TABLE RLS POLICIES
-- =============================================================================

-- All authenticated users can view organization settings
CREATE POLICY "organization_settings_select_policy" ON organization_settings
  FOR SELECT
  USING (auth.uid() IS NOT NULL);

-- Only admins can create organization settings
CREATE POLICY "organization_settings_insert_policy" ON organization_settings
  FOR INSERT
  WITH CHECK (user_has_role(auth.uid(), 'admin'));

-- Only admins can update organization settings
CREATE POLICY "organization_settings_update_policy" ON organization_settings
  FOR UPDATE
  USING (user_has_role(auth.uid(), 'admin'))
  WITH CHECK (user_has_role(auth.uid(), 'admin'));

-- Only admins can delete organization settings
CREATE POLICY "organization_settings_delete_policy" ON organization_settings
  FOR DELETE
  USING (user_has_role(auth.uid(), 'admin'));

-- =============================================================================
-- AUDIT_LOG TABLE RLS POLICIES
-- =============================================================================

-- Only admins can view audit logs
CREATE POLICY "audit_log_select_policy" ON audit_log
  FOR SELECT
  USING (user_has_role(auth.uid(), 'admin'));

-- Audit log entries are created automatically via triggers
-- No manual insert/update/delete policies needed as this should be system-managed

-- =============================================================================
-- SPECIAL POLICIES FOR PUBLIC ACCESS
-- =============================================================================

-- Create policies for unauthenticated access to newsletter subscription
-- This allows the public website to subscribe users to the newsletter

-- Allow anonymous newsletter subscription creation (for public website)
CREATE POLICY "newsletter_subscribers_public_insert" ON newsletter_subscribers
  FOR INSERT
  WITH CHECK (true); -- Allow anyone to subscribe

-- Allow anonymous newsletter confirmation (for email confirmation links)
-- This policy allows updates only for confirmation-related operations
CREATE POLICY "newsletter_subscribers_public_confirm" ON newsletter_subscribers
  FOR UPDATE
  USING (
    -- Allow updates to records that have a confirmation token
    confirmation_token IS NOT NULL AND
    -- And either no user is authenticated (public confirmation) or user has publisher role
    (auth.uid() IS NULL OR user_has_role(auth.uid(), 'publisher'))
  )
  WITH CHECK (
    -- Allow the update if no user is authenticated (public confirmation)
    -- or if user has publisher role
    auth.uid() IS NULL OR user_has_role(auth.uid(), 'publisher')
  );

-- =============================================================================
-- ENABLE RLS ON ALL TABLES
-- =============================================================================

-- Ensure RLS is enabled on all tables (some may already be enabled in table creation)
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE invitations ENABLE ROW LEVEL SECURITY;
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE post_tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE post_versions ENABLE ROW LEVEL SECURITY;
ALTER TABLE pages ENABLE ROW LEVEL SECURITY;
ALTER TABLE biographies ENABLE ROW LEVEL SECURITY;
ALTER TABLE newsletter_subscribers ENABLE ROW LEVEL SECURITY;
ALTER TABLE organization_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_log ENABLE ROW LEVEL SECURITY;

-- =============================================================================
-- SCRIPT COMPLETION
-- =============================================================================

-- Log successful completion
DO $$
BEGIN
  RAISE NOTICE 'Row Level Security policies created successfully at %', NOW();
END $$;
