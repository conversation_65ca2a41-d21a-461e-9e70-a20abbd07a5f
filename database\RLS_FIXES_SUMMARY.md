# RLS Policy Fixes Summary

## Overview

This document summarizes the fixes made to the Row Level Security (RLS) policies in `database/06_rls_policies.sql` to correct invalid `OLD` and `NEW` variable references that are not supported in PostgreSQL RLS contexts.

## Issues Found and Fixed

### 1. Invalid OLD Variable Reference in Profiles Policy

**Location**: `profiles_update_policy` (Line 31)

**Original Code**:
```sql
WITH CHECK (
  -- Users cannot change their own role
  (id = auth.uid() AND role = OLD.role) OR
  -- Admins can change any role
  user_has_role(auth.uid(), 'admin')
);
```

**Problem**: RLS policies cannot access `OLD.role` to compare previous values.

**Solution**: Replaced with database trigger approach:
1. **Simplified RLS Policy**: Removed the `OLD.role` reference
2. **Added Database Function**: `prevent_role_change()` in `04_functions.sql`
3. **Added Database Trigger**: `trigger_prevent_role_change` in `05_triggers.sql`

**New Code**:
```sql
-- RLS Policy (simplified)
WITH CHECK (
  user_has_role(auth.uid(), 'admin') OR
  id = auth.uid()
);

-- Database Function (handles role protection)
CREATE OR REPLACE FUNCTION prevent_role_change()
RETURNS TRIGGER AS $$
DECLARE
  current_user_role user_role;
BEGIN
  SELECT role INTO current_user_role
  FROM profiles
  WHERE id = auth.uid() AND deleted_at IS NULL;
  
  IF OLD.role != NEW.role AND current_user_role != 'admin' THEN
    RAISE EXCEPTION 'Only administrators can change user roles';
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

### 2. Invalid OLD/NEW Variable References in Newsletter Policy

**Location**: `newsletter_subscribers_public_confirm` (Lines 403-405)

**Original Code**:
```sql
WITH CHECK (
  -- Only allow updating confirmation-related fields
  OLD.email = NEW.email AND
  OLD.first_name = NEW.first_name AND
  OLD.last_name = NEW.last_name
);
```

**Problem**: RLS policies cannot access `OLD` and `NEW` variables to compare field changes.

**Solution**: Replaced with database trigger approach:
1. **Simplified RLS Policy**: Removed OLD/NEW references, focused on access control
2. **Added Database Function**: `validate_newsletter_update()` in `04_functions.sql`
3. **Added Database Trigger**: `trigger_validate_newsletter_update` in `05_triggers.sql`

**New Code**:
```sql
-- RLS Policy (simplified)
WITH CHECK (
  auth.uid() IS NULL OR user_has_role(auth.uid(), 'publisher')
);

-- Database Function (handles field validation)
CREATE OR REPLACE FUNCTION validate_newsletter_update()
RETURNS TRIGGER AS $$
BEGIN
  IF auth.uid() IS NULL THEN
    IF OLD.email != NEW.email OR 
       OLD.first_name != NEW.first_name OR 
       OLD.last_name != NEW.last_name THEN
      RAISE EXCEPTION 'Public confirmation can only update confirmation status, not personal information';
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

## Architecture Changes

### Before (Problematic Approach)
- RLS policies attempted to use `OLD` and `NEW` variables
- Field-level validation mixed with access control in RLS
- Invalid PostgreSQL syntax

### After (Correct Approach)
- **RLS Policies**: Focus on access control (who can access what)
- **Database Triggers**: Handle field-level validation and business rules
- **Clear Separation**: Access control vs. data validation concerns
- **Valid PostgreSQL Syntax**: All policies use proper RLS context

## Benefits of the New Approach

1. **Correct Syntax**: All RLS policies use valid PostgreSQL syntax
2. **Better Separation of Concerns**: 
   - RLS = Access control
   - Triggers = Data validation
3. **More Maintainable**: Easier to understand and modify
4. **Better Performance**: RLS policies are simpler and faster
5. **Comprehensive Protection**: Both access control and data validation

## Testing

Added `test_rls_policies.sql` script that:
- Verifies all policies exist and are syntactically valid
- Checks for any remaining OLD/NEW references
- Validates RLS is enabled on all tables
- Tests basic policy functionality
- Ensures all required functions exist

## Files Modified

1. **`04_functions.sql`**: Added security functions
   - `prevent_role_change()`
   - `validate_newsletter_update()`

2. **`05_triggers.sql`**: Added security triggers
   - `trigger_prevent_role_change`
   - `trigger_validate_newsletter_update`

3. **`06_rls_policies.sql`**: Fixed RLS policies
   - Removed invalid OLD/NEW references
   - Simplified policy logic
   - Focused on access control

4. **`test_rls_policies.sql`**: Added validation script
   - Tests for OLD/NEW references
   - Validates policy syntax
   - Checks RLS coverage

5. **`99_execute_all.sql`**: Updated master script
   - Includes test execution
   - Updated completion messages

6. **`README.md`**: Updated documentation
   - Added troubleshooting section
   - Documented security improvements
   - Added test script reference

## Security Implications

### Maintained Security Features
- ✅ Role-based access control still enforced
- ✅ Users cannot change their own roles
- ✅ Newsletter confirmation validation preserved
- ✅ All original security intentions maintained

### Improved Security
- ✅ More robust error handling
- ✅ Clearer security boundaries
- ✅ Better audit trail through triggers
- ✅ Comprehensive testing framework

## Migration Notes

If you have an existing database with the old policies:

1. **Drop old policies** with OLD/NEW references
2. **Run the updated scripts** in order
3. **Test thoroughly** with the validation script
4. **Verify application functionality** with different user roles

## Conclusion

The RLS policy fixes ensure that:
- All policies use valid PostgreSQL syntax
- Security requirements are fully maintained
- Code is more maintainable and testable
- Performance is optimized
- Best practices are followed for RLS and triggers
